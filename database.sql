-- Netflix-like Movie Website Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS netflixapp;
USE netflixapp;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    is_admin TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Movies table
CREATE TABLE movies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    genre VARCHAR(100),
    release_year YEAR,
    duration INT, -- in minutes
    rating DECIMAL(3,1) DEFAULT 0.0,
    poster_image VARCHAR(255),
    backdrop_image VARCHAR(255),
    video_url VARCHAR(255),
    trailer_url VARCHAR(255),
    featured TINYINT(1) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User favorites table
CREATE TABLE user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    movie_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, movie_id)
);

-- Movie categories table
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Movie categories relationship
CREATE TABLE movie_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movie_id INT NOT NULL,
    category_id INT NOT NULL,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_movie_category (movie_id, category_id)
);

-- User watch history
CREATE TABLE watch_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    movie_id INT NOT NULL,
    watch_time INT DEFAULT 0, -- in seconds
    completed TINYINT(1) DEFAULT 0,
    last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_movie (user_id, movie_id)
);

-- Insert default categories
INSERT INTO categories (name, description) VALUES
('Action', 'Action and adventure movies'),
('Comedy', 'Comedy and humor movies'),
('Drama', 'Drama and emotional movies'),
('Horror', 'Horror and thriller movies'),
('Romance', 'Romance and love movies'),
('Sci-Fi', 'Science fiction movies'),
('Documentary', 'Documentary films'),
('Animation', 'Animated movies'),
('Crime', 'Crime and mystery movies'),
('Fantasy', 'Fantasy and magical movies');

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password, full_name, is_admin) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 1);

-- Insert sample movies
INSERT INTO movies (title, description, genre, release_year, duration, rating, poster_image, backdrop_image, video_url, featured) VALUES
('The Matrix', 'A computer programmer discovers that reality as he knows it is a simulation controlled by machines.', 'Sci-Fi', 1999, 136, 8.7, 'matrix-poster.jpg', 'matrix-backdrop.jpg', 'matrix-trailer.mp4', 1),
('Inception', 'A thief who steals corporate secrets through dream-sharing technology is given the inverse task of planting an idea.', 'Sci-Fi', 2010, 148, 8.8, 'inception-poster.jpg', 'inception-backdrop.jpg', 'inception-trailer.mp4', 1),
('The Dark Knight', 'Batman faces the Joker, a criminal mastermind who wants to plunge Gotham City into anarchy.', 'Action', 2008, 152, 9.0, 'dark-knight-poster.jpg', 'dark-knight-backdrop.jpg', 'dark-knight-trailer.mp4', 1),
('Pulp Fiction', 'The lives of two mob hitmen, a boxer, a gangster and his wife intertwine in four tales of violence and redemption.', 'Crime', 1994, 154, 8.9, 'pulp-fiction-poster.jpg', 'pulp-fiction-backdrop.jpg', 'pulp-fiction-trailer.mp4', 0),
('Forrest Gump', 'The presidencies of Kennedy and Johnson through the eyes of an Alabama man with an IQ of 75.', 'Drama', 1994, 142, 8.8, 'forrest-gump-poster.jpg', 'forrest-gump-backdrop.jpg', 'forrest-gump-trailer.mp4', 0);
