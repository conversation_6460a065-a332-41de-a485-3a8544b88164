<?php
requireLogin();
$pageTitle = 'My List';
require_once 'includes/header.php';

// Get user's favorite movies
$favoriteMovies = getUserFavorites($_SESSION['user_id']);
?>

<div class="my-list-page" style="padding-top: 100px; min-height: 80vh;">
    <div style="padding: 2rem 4%;">
        <div class="page-header" style="margin-bottom: 3rem;">
            <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem;">My List</h1>
            <p style="color: #b3b3b3; font-size: 1.1rem;">Movies you've added to your favorites</p>
        </div>

        <?php if (!empty($favoriteMovies)): ?>
            <div class="favorites-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 2rem;">
                <?php foreach ($favoriteMovies as $movie): ?>
                <div class="movie-card" data-movie-id="<?php echo $movie->id; ?>" style="position: relative;">
                    <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($movie->title); ?>" 
                         class="movie-poster">
                    <div class="movie-info">
                        <h3 class="movie-title"><?php echo htmlspecialchars($movie->title); ?></h3>
                        <div class="movie-meta">
                            <span><i class="fas fa-calendar"></i> <?php echo $movie->release_year; ?></span>
                            <span><i class="fas fa-clock"></i> <?php echo formatDuration($movie->duration); ?></span>
                            <span><i class="fas fa-star"></i> <?php echo $movie->rating; ?></span>
                        </div>
                        <p style="font-size: 0.8rem; color: #b3b3b3; margin-top: 0.5rem; line-height: 1.4;">
                            <?php echo htmlspecialchars(substr($movie->description, 0, 100)) . '...'; ?>
                        </p>
                        <div class="movie-actions" style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                            <a href="movie.php?id=<?php echo $movie->id; ?>" class="btn btn-primary" style="font-size: 0.8rem; padding: 8px 12px;">
                                <i class="fas fa-play"></i> Watch
                            </a>
                            <button class="favorite-btn favorited"
                                    data-movie-id="<?php echo $movie->id; ?>"
                                    style="background: none; border: none; font-size: 1.2rem; cursor: pointer; color: #e50914;">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="list-stats" style="margin-top: 3rem; padding: 2rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                <h3 style="margin-bottom: 1rem; color: #e50914;">Your Stats</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem;">
                    <div class="stat-item">
                        <div style="font-size: 2rem; font-weight: bold; color: #e50914;"><?php echo count($favoriteMovies); ?></div>
                        <div style="color: #b3b3b3;">Movies in Your List</div>
                    </div>
                    <div class="stat-item">
                        <?php 
                        $totalDuration = array_sum(array_column($favoriteMovies, 'duration'));
                        $hours = floor($totalDuration / 60);
                        $minutes = $totalDuration % 60;
                        ?>
                        <div style="font-size: 2rem; font-weight: bold; color: #e50914;"><?php echo $hours; ?>h <?php echo $minutes; ?>m</div>
                        <div style="color: #b3b3b3;">Total Runtime</div>
                    </div>
                    <div class="stat-item">
                        <?php 
                        $genres = array_unique(array_column($favoriteMovies, 'genre'));
                        ?>
                        <div style="font-size: 2rem; font-weight: bold; color: #e50914;"><?php echo count($genres); ?></div>
                        <div style="color: #b3b3b3;">Different Genres</div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="empty-list" style="text-align: center; padding: 4rem 0;">
                <div style="font-size: 4rem; margin-bottom: 1rem; color: var(--text-muted);"><i class="far fa-heart"></i></div>
                <h2 style="margin-bottom: 1rem;">Your list is empty</h2>
                <p style="color: #b3b3b3; margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
                    Start building your personal movie collection by adding movies to your list.
                    Click the heart icon on any movie to add it to your favorites.
                </p>
                <a href="index.php" class="btn btn-primary"><i class="fas fa-search"></i> Browse Movies</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.movie-card:hover {
    transform: scale(1.05);
    transition: transform 0.3s;
}

@media (max-width: 768px) {
    .favorites-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .list-stats {
        margin-top: 2rem;
        padding: 1rem;
    }
    
    .list-stats > div {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
