    </main>

    <footer class="footer" style="background: #141414; padding: 3rem 4%; margin-top: 4rem; border-top: 1px solid #333;">
        <div class="footer-content" style="max-width: 1200px; margin: 0 auto;">
            <div class="footer-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div class="footer-section">
                    <h3 style="color: #e50914; margin-bottom: 1rem; font-size: 1.2rem;">NetflixApp</h3>
                    <p style="color: #b3b3b3; line-height: 1.6;">
                        Your ultimate destination for streaming movies and TV shows. 
                        Watch unlimited content anytime, anywhere.
                    </p>
                </div>
                
                <div class="footer-section">
                    <h4 style="color: white; margin-bottom: 1rem;">Quick Links</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 0.5rem;"><a href="index.php" style="color: #b3b3b3; text-decoration: none;">Home</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="movies.php" style="color: #b3b3b3; text-decoration: none;">Movies</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="categories.php" style="color: #b3b3b3; text-decoration: none;">Categories</a></li>
                        <?php if (isLoggedIn()): ?>
                        <li style="margin-bottom: 0.5rem;"><a href="my-list.php" style="color: #b3b3b3; text-decoration: none;">My List</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 style="color: white; margin-bottom: 1rem;">Account</h4>
                    <ul style="list-style: none;">
                        <?php if (isLoggedIn()): ?>
                        <li style="margin-bottom: 0.5rem;"><a href="profile.php" style="color: #b3b3b3; text-decoration: none;">Profile</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="settings.php" style="color: #b3b3b3; text-decoration: none;">Settings</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="watch-history.php" style="color: #b3b3b3; text-decoration: none;">Watch History</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="logout.php" style="color: #b3b3b3; text-decoration: none;">Sign Out</a></li>
                        <?php else: ?>
                        <li style="margin-bottom: 0.5rem;"><a href="login.php" style="color: #b3b3b3; text-decoration: none;">Sign In</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="register.php" style="color: #b3b3b3; text-decoration: none;">Sign Up</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 style="color: white; margin-bottom: 1rem;">Support</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 0.5rem;"><a href="help.php" style="color: #b3b3b3; text-decoration: none;">Help Center</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="contact.php" style="color: #b3b3b3; text-decoration: none;">Contact Us</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="privacy.php" style="color: #b3b3b3; text-decoration: none;">Privacy Policy</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="terms.php" style="color: #b3b3b3; text-decoration: none;">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid #333; padding-top: 2rem; text-align: center;">
                <div class="social-links" style="margin-bottom: 1rem;">
                    <a href="#" style="color: #b3b3b3; margin: 0 1rem; font-size: 1.5rem; text-decoration: none;">📘</a>
                    <a href="#" style="color: #b3b3b3; margin: 0 1rem; font-size: 1.5rem; text-decoration: none;">🐦</a>
                    <a href="#" style="color: #b3b3b3; margin: 0 1rem; font-size: 1.5rem; text-decoration: none;">📷</a>
                    <a href="#" style="color: #b3b3b3; margin: 0 1rem; font-size: 1.5rem; text-decoration: none;">📺</a>
                </div>
                
                <p style="color: #666; font-size: 0.9rem;">
                    &copy; <?php echo date('Y'); ?> NetflixApp. All rights reserved. | 
                    Made with ❤️ for movie lovers
                </p>
                
                <div class="footer-info" style="margin-top: 1rem; font-size: 0.8rem; color: #666;">
                    <p>
                        This is a demo movie streaming website. 
                        All movie content is for demonstration purposes only.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $jsFile): ?>
            <script src="<?php echo $jsFile; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>
</body>
</html>
