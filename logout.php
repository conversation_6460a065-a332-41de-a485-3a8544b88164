<?php
require_once 'includes/functions.php';

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Clear remember token from database
    if (isLoggedIn()) {
        global $database;
        $database->query('UPDATE users SET remember_token = NULL WHERE id = :id');
        $database->bind(':id', $_SESSION['user_id']);
        $database->execute();
    }
}

// Destroy session
session_destroy();

// Redirect to login page with success message
session_start();
setFlashMessage('success', 'You have been successfully logged out.');
header('Location: login.php');
exit();
?>
