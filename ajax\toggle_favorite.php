<?php
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to add favorites']);
    exit();
}

// Check if request is POST and has JSON content
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['movie_id']) || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit();
}

$movieId = (int)$input['movie_id'];
$action = $input['action'];
$userId = $_SESSION['user_id'];

// Validate movie exists
$movie = getMovieById($movieId);
if (!$movie) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Movie not found']);
    exit();
}

try {
    if ($action === 'add') {
        if (isMovieInFavorites($userId, $movieId)) {
            echo json_encode(['success' => false, 'message' => 'Movie already in favorites']);
        } else {
            if (addToFavorites($userId, $movieId)) {
                echo json_encode(['success' => true, 'message' => 'Added to favorites']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to add to favorites']);
            }
        }
    } elseif ($action === 'remove') {
        if (!isMovieInFavorites($userId, $movieId)) {
            echo json_encode(['success' => false, 'message' => 'Movie not in favorites']);
        } else {
            if (removeFromFavorites($userId, $movieId)) {
                echo json_encode(['success' => true, 'message' => 'Removed from favorites']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to remove from favorites']);
            }
        }
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
