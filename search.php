<?php
$pageTitle = 'Search Results';
require_once 'includes/header.php';

$searchQuery = isset($_GET['q']) ? sanitize($_GET['q']) : '';
$movies = [];

if ($searchQuery) {
    $movies = searchMovies($searchQuery);
    $pageTitle = 'Search Results for "' . $searchQuery . '"';
}
?>

<div class="search-page" style="padding-top: 100px; min-height: 80vh;">
    <div style="padding: 2rem 4%;">
        <div class="search-header" style="margin-bottom: 2rem;">
            <h1 style="font-size: 2rem; margin-bottom: 1rem;">
                <?php if ($searchQuery): ?>
                    Search Results for "<?php echo htmlspecialchars($searchQuery); ?>"
                <?php else: ?>
                    Search Movies
                <?php endif; ?>
            </h1>
            
            <!-- Search Form -->
            <form method="GET" action="search.php" style="margin-bottom: 2rem;">
                <div style="display: flex; gap: 1rem; max-width: 600px;">
                    <input type="text" name="q" value="<?php echo htmlspecialchars($searchQuery); ?>" 
                           placeholder="Search for movies..." 
                           style="flex: 1; padding: 12px 15px; background: #333; border: 1px solid #555; border-radius: 4px; color: white; font-size: 1rem;">
                    <button type="submit" class="btn btn-primary" style="padding: 12px 24px;">Search</button>
                </div>
            </form>
        </div>

        <?php if ($searchQuery): ?>
            <?php if (!empty($movies)): ?>
                <div class="search-results">
                    <p style="color: #b3b3b3; margin-bottom: 2rem;">
                        Found <?php echo count($movies); ?> result<?php echo count($movies) !== 1 ? 's' : ''; ?>
                    </p>
                    
                    <div class="movies-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 2rem;">
                        <?php foreach ($movies as $movie): ?>
                        <div class="movie-card" data-movie-id="<?php echo $movie->id; ?>" style="position: relative;">
                            <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                                 alt="<?php echo htmlspecialchars($movie->title); ?>" 
                                 class="movie-poster">
                            <div class="movie-info">
                                <h3 class="movie-title"><?php echo htmlspecialchars($movie->title); ?></h3>
                                <div class="movie-meta">
                                    <span><?php echo $movie->release_year; ?></span>
                                    <span><?php echo formatDuration($movie->duration); ?></span>
                                    <span>⭐ <?php echo $movie->rating; ?></span>
                                </div>
                                <p style="font-size: 0.8rem; color: #b3b3b3; margin-top: 0.5rem; line-height: 1.4;">
                                    <?php echo htmlspecialchars(substr($movie->description, 0, 100)) . '...'; ?>
                                </p>
                                <?php if (isLoggedIn()): ?>
                                <button class="favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? 'favorited' : ''; ?>" 
                                        data-movie-id="<?php echo $movie->id; ?>"
                                        style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 1.5rem; cursor: pointer;">
                                    <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? '❤️' : '🤍'; ?>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="no-results" style="text-align: center; padding: 4rem 0;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">🔍</div>
                    <h2 style="margin-bottom: 1rem;">No movies found</h2>
                    <p style="color: #b3b3b3; margin-bottom: 2rem;">
                        We couldn't find any movies matching "<?php echo htmlspecialchars($searchQuery); ?>".
                    </p>
                    <div>
                        <p style="color: #b3b3b3; margin-bottom: 1rem;">Try searching for:</p>
                        <ul style="list-style: none; color: #b3b3b3;">
                            <li>• Different keywords</li>
                            <li>• Movie titles or genres</li>
                            <li>• Actor or director names</li>
                            <li>• Release years</li>
                        </ul>
                    </div>
                    <a href="index.php" class="btn btn-primary" style="margin-top: 2rem;">Browse All Movies</a>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Popular Searches or Categories -->
            <div class="search-suggestions">
                <h2 style="margin-bottom: 2rem;">Popular Categories</h2>
                <div class="categories-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <?php 
                    $categories = getAllCategories();
                    foreach ($categories as $category): 
                    ?>
                    <a href="search.php?q=<?php echo urlencode($category->name); ?>" 
                       class="category-card" 
                       style="display: block; padding: 2rem; background: linear-gradient(135deg, #333, #444); border-radius: 8px; text-decoration: none; color: white; transition: transform 0.3s;">
                        <h3 style="margin-bottom: 0.5rem; color: #e50914;"><?php echo htmlspecialchars($category->name); ?></h3>
                        <p style="color: #b3b3b3; font-size: 0.9rem;"><?php echo htmlspecialchars($category->description); ?></p>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.category-card:hover {
    transform: translateY(-5px);
}

@media (max-width: 768px) {
    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
