# NetflixApp - Movie Streaming Website

A Netflix-like movie streaming website built with PHP, HTML, CSS, JavaScript, and MySQL.

## Features

- 🎬 **Movie Browsing**: Browse movies by categories and genres
- 🔍 **Search Functionality**: Search for movies by title, genre, or description
- 👤 **User Authentication**: Register, login, and user profile management
- ❤️ **Favorites System**: Add/remove movies to/from personal favorites list
- 📺 **Video Player**: Built-in video player with progress tracking
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 🎯 **Personalized Experience**: Continue watching and recommendations
- 👑 **Admin Panel**: Admin interface for managing movies and users
- 🔐 **Secure**: Password hashing, SQL injection protection, XSS prevention

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Server**: Apache (XAMPP recommended for development)

## Installation

### Prerequisites
- XAMPP (or LAMP/WAMP) with PHP 7.4+ and MySQL 5.7+
- Web browser (Chrome, Firefox, Safari, Edge)

### Setup Instructions

1. **Download and Install XAMPP**
   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Clone/Download the Project**
   ```bash
   # If using Git
   git clone [repository-url] C:\xampp\htdocs\netflixapp
   
   # Or download and extract to C:\xampp\htdocs\netflixapp
   ```

3. **Database Setup**
   - Open phpMyAdmin: [http://localhost/phpmyadmin](http://localhost/phpmyadmin)
   - Create a new database named `netflixapp`
   - Import the `database.sql` file or run the SQL commands from it

4. **Configuration**
   - Open `config/database.php`
   - Update database credentials if needed (default: root/no password)

5. **File Permissions**
   - Ensure `assets/images/` and `assets/videos/` directories are writable
   - Create upload directories if they don't exist

6. **Access the Website**
   - Open your browser and go to: [http://localhost/netflixapp](http://localhost/netflixapp)

## Default Login Credentials

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

## Project Structure

```
netflixapp/
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── script.js
│   ├── images/
│   │   └── (movie posters, backdrops, avatars)
│   └── videos/
│       └── (movie files, trailers)
├── config/
│   └── database.php
├── includes/
│   ├── functions.php
│   ├── header.php
│   └── footer.php
├── ajax/
│   ├── toggle_favorite.php
│   └── update_watch_progress.php
├── admin/
│   └── (admin panel files)
├── database.sql
├── index.php
├── login.php
├── register.php
├── logout.php
├── movie.php
├── search.php
├── my-list.php
└── README.md
```

## Key Features Explained

### User Authentication
- Secure registration and login system
- Password hashing using PHP's `password_hash()`
- Session management
- Remember me functionality
- Profile management

### Movie Management
- Movie catalog with detailed information
- Category-based organization
- Featured movies section
- Search functionality
- Movie ratings and reviews

### Favorites System
- Add/remove movies to personal list
- AJAX-powered for smooth user experience
- Persistent storage in database

### Video Player
- HTML5 video player
- Progress tracking
- Continue watching functionality
- Keyboard shortcuts support

### Responsive Design
- Mobile-first approach
- Netflix-inspired UI/UX
- Smooth animations and transitions
- Touch-friendly interface

## Customization

### Adding Movies
1. Access admin panel (login as admin)
2. Go to "Add Movie" section
3. Fill in movie details
4. Upload poster and backdrop images
5. Add video file (optional for demo)

### Styling
- Main styles in `assets/css/style.css`
- Netflix-like color scheme (#e50914 red, #141414 dark)
- Easily customizable CSS variables

### Database Schema
- `users`: User accounts and profiles
- `movies`: Movie information and metadata
- `categories`: Movie categories/genres
- `user_favorites`: User's favorite movies
- `watch_history`: Viewing progress tracking

## Security Features

- SQL injection prevention using prepared statements
- XSS protection with input sanitization
- CSRF protection for forms
- Secure password hashing
- Session security measures

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Development

### Adding New Features
1. Create new PHP files in appropriate directories
2. Update navigation in `includes/header.php`
3. Add database tables if needed
4. Update `includes/functions.php` for new functionality

### Database Changes
1. Update `database.sql` with new schema
2. Add migration scripts if needed
3. Update functions in `includes/functions.php`

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check XAMPP MySQL service is running
   - Verify database credentials in `config/database.php`
   - Ensure `netflixapp` database exists

2. **Images Not Loading**
   - Check file paths in `assets/images/`
   - Verify file permissions
   - Use placeholder images for testing

3. **Videos Not Playing**
   - Ensure video files are in correct format (MP4)
   - Check file paths and permissions
   - Use sample videos for testing

4. **Login Issues**
   - Clear browser cache and cookies
   - Check database for user records
   - Verify password hashing

## License

This project is for educational purposes. Please ensure you have proper licensing for any movie content used.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the code comments
3. Check browser console for JavaScript errors
4. Verify database connections and queries

---

**Note**: This is a demo application for learning purposes. For production use, implement additional security measures, optimize for performance, and ensure proper content licensing.
