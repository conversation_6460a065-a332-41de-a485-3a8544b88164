<?php
require_once 'includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - NetflixApp' : 'NetflixApp - Watch Movies Online'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Watch unlimited movies and TV shows online. Stream anywhere, anytime.'; ?>">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="index.php" class="logo">NetflixApp</a>
            
            <?php if (isLoggedIn()): ?>
            <ul class="nav-links">
                <li><a href="index.php">Home</a></li>
                <li><a href="movies.php">Movies</a></li>
                <li><a href="categories.php">Categories</a></li>
                <li><a href="my-list.php">My List</a></li>
                <?php if (isAdmin()): ?>
                <li><a href="admin/">Admin</a></li>
                <?php endif; ?>
            </ul>
            
            <div class="user-menu">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search movies..." id="searchInput">
                </div>
                
                <div class="profile-dropdown">
                    <button class="profile-btn">
                        <img src="assets/images/<?php echo $_SESSION['profile_image'] ?? 'default-avatar.png'; ?>" 
                             alt="Profile" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                        <span>▼</span>
                    </button>
                    <div class="dropdown-menu">
                        <a href="profile.php">Profile</a>
                        <a href="my-list.php">My List</a>
                        <a href="watch-history.php">Watch History</a>
                        <a href="settings.php">Settings</a>
                        <hr style="border-color: #333; margin: 5px 0;">
                        <a href="logout.php">Sign Out</a>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="user-menu">
                <a href="login.php" class="btn btn-secondary">Sign In</a>
                <a href="register.php" class="btn btn-primary">Sign Up</a>
            </div>
            <?php endif; ?>
        </nav>
    </header>

    <?php
    // Display flash messages
    $flashMessage = getFlashMessage();
    if ($flashMessage):
    ?>
    <div class="alert alert-<?php echo $flashMessage['type']; ?>" style="margin-top: 80px;">
        <?php echo htmlspecialchars($flashMessage['message']); ?>
    </div>
    <?php endif; ?>

    <main class="main-content">
