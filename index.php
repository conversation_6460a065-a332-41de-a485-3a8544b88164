<?php
$pageTitle = 'Home';
$pageDescription = 'Watch unlimited movies and TV shows online. Stream anywhere, anytime.';
require_once 'includes/header.php';

// Get featured movies
$featuredMovies = getFeaturedMovies();
$featuredMovie = !empty($featuredMovies) ? $featuredMovies[0] : null;

// Get movies by categories
$categories = getAllCategories();
?>

<?php if ($featuredMovie): ?>
<!-- Hero Section -->
<section class="hero" style="background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7)), url('assets/images/<?php echo $featuredMovie->backdrop_image ?? 'default-backdrop.jpg'; ?>') center/cover;">
    <div class="hero-content">
        <h1 class="hero-title"><?php echo htmlspecialchars($featuredMovie->title); ?></h1>
        <p class="hero-description"><?php echo htmlspecialchars(substr($featuredMovie->description, 0, 200)) . '...'; ?></p>
        <div class="hero-meta">
            <span><?php echo $featuredMovie->release_year; ?></span>
            <span><?php echo formatDuration($featuredMovie->duration); ?></span>
            <span>⭐ <?php echo $featuredMovie->rating; ?></span>
        </div>
        <div class="hero-buttons">
            <a href="movie.php?id=<?php echo $featuredMovie->id; ?>" class="btn btn-primary">
                ▶️ Play
            </a>
            <a href="movie.php?id=<?php echo $featuredMovie->id; ?>" class="btn btn-secondary">
                ℹ️ More Info
            </a>
            <?php if (isLoggedIn()): ?>
            <button class="btn btn-secondary favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $featuredMovie->id) ? 'favorited' : ''; ?>" 
                    data-movie-id="<?php echo $featuredMovie->id; ?>">
                <?php echo isMovieInFavorites($_SESSION['user_id'], $featuredMovie->id) ? '❤️' : '🤍'; ?> My List
            </button>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Movies Section -->
<?php if (!empty($featuredMovies)): ?>
<section class="movie-section">
    <h2 class="section-title">Featured Movies</h2>
    <div class="movie-row">
        <?php foreach ($featuredMovies as $movie): ?>
        <div class="movie-card" data-movie-id="<?php echo $movie->id; ?>">
            <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                 alt="<?php echo htmlspecialchars($movie->title); ?>" 
                 class="movie-poster">
            <div class="movie-info">
                <h3 class="movie-title"><?php echo htmlspecialchars($movie->title); ?></h3>
                <div class="movie-meta">
                    <span><?php echo $movie->release_year; ?></span>
                    <span><?php echo formatDuration($movie->duration); ?></span>
                    <span>⭐ <?php echo $movie->rating; ?></span>
                </div>
                <?php if (isLoggedIn()): ?>
                <button class="favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? 'favorited' : ''; ?>" 
                        data-movie-id="<?php echo $movie->id; ?>"
                        style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 1.5rem; cursor: pointer;">
                    <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? '❤️' : '🤍'; ?>
                </button>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</section>
<?php endif; ?>

<!-- Movies by Categories -->
<?php foreach ($categories as $category): ?>
    <?php 
    $categoryMovies = getMoviesByCategory($category->id, 10);
    if (!empty($categoryMovies)): 
    ?>
    <section class="movie-section">
        <h2 class="section-title"><?php echo htmlspecialchars($category->name); ?></h2>
        <div class="movie-row">
            <?php foreach ($categoryMovies as $movie): ?>
            <div class="movie-card" data-movie-id="<?php echo $movie->id; ?>">
                <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                     alt="<?php echo htmlspecialchars($movie->title); ?>" 
                     class="movie-poster">
                <div class="movie-info">
                    <h3 class="movie-title"><?php echo htmlspecialchars($movie->title); ?></h3>
                    <div class="movie-meta">
                        <span><?php echo $movie->release_year; ?></span>
                        <span><?php echo formatDuration($movie->duration); ?></span>
                        <span>⭐ <?php echo $movie->rating; ?></span>
                    </div>
                    <?php if (isLoggedIn()): ?>
                    <button class="favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? 'favorited' : ''; ?>" 
                            data-movie-id="<?php echo $movie->id; ?>"
                            style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 1.5rem; cursor: pointer;">
                        <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? '❤️' : '🤍'; ?>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php endif; ?>
<?php endforeach; ?>

<!-- Continue Watching Section (for logged in users) -->
<?php if (isLoggedIn()): ?>
    <?php 
    $watchHistory = getUserWatchHistory($_SESSION['user_id'], 10);
    if (!empty($watchHistory)): 
    ?>
    <section class="movie-section">
        <h2 class="section-title">Continue Watching</h2>
        <div class="movie-row">
            <?php foreach ($watchHistory as $movie): ?>
            <div class="movie-card" data-movie-id="<?php echo $movie->id; ?>">
                <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                     alt="<?php echo htmlspecialchars($movie->title); ?>" 
                     class="movie-poster">
                <div class="movie-info">
                    <h3 class="movie-title"><?php echo htmlspecialchars($movie->title); ?></h3>
                    <div class="movie-meta">
                        <span><?php echo $movie->release_year; ?></span>
                        <span><?php echo formatDuration($movie->duration); ?></span>
                        <span>⭐ <?php echo $movie->rating; ?></span>
                    </div>
                    <div class="watch-progress" style="background: #333; height: 4px; margin-top: 5px; border-radius: 2px;">
                        <div class="progress-bar" style="background: #e50914; height: 100%; width: <?php echo ($movie->watch_time / ($movie->duration * 60)) * 100; ?>%; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php endif; ?>
<?php endif; ?>

<!-- Welcome Section for Non-logged Users -->
<?php if (!isLoggedIn()): ?>
<section class="welcome-section" style="padding: 4rem; text-align: center; background: linear-gradient(135deg, #141414, #1a1a1a);">
    <div style="max-width: 800px; margin: 0 auto;">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: #e50914;">Welcome to NetflixApp</h2>
        <p style="font-size: 1.2rem; margin-bottom: 2rem; color: #b3b3b3; line-height: 1.6;">
            Discover thousands of movies and TV shows. Watch anywhere, anytime. 
            Create your account today and start your entertainment journey.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="register.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 15px 30px;">
                Get Started
            </a>
            <a href="login.php" class="btn btn-secondary" style="font-size: 1.1rem; padding: 15px 30px;">
                Sign In
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section" style="padding: 4rem; background: #0a0a0a;">
    <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="text-align: center; font-size: 2rem; margin-bottom: 3rem;">Why Choose NetflixApp?</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
            <div style="text-align: center; padding: 2rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🎬</div>
                <h3 style="margin-bottom: 1rem; color: #e50914;">Unlimited Movies</h3>
                <p style="color: #b3b3b3; line-height: 1.6;">
                    Access thousands of movies across all genres. From blockbusters to indie films.
                </p>
            </div>
            <div style="text-align: center; padding: 2rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📱</div>
                <h3 style="margin-bottom: 1rem; color: #e50914;">Watch Anywhere</h3>
                <p style="color: #b3b3b3; line-height: 1.6;">
                    Stream on your phone, tablet, laptop, or TV. Your entertainment, your way.
                </p>
            </div>
            <div style="text-align: center; padding: 2rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">⭐</div>
                <h3 style="margin-bottom: 1rem; color: #e50914;">Personalized</h3>
                <p style="color: #b3b3b3; line-height: 1.6;">
                    Get recommendations based on your viewing history and preferences.
                </p>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
