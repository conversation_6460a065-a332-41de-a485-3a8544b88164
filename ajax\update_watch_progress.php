<?php
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in']);
    exit();
}

// Check if request is POST and has JSON content
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['movie_id']) || !isset($input['watch_time'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit();
}

$movieId = (int)$input['movie_id'];
$watchTime = (int)$input['watch_time'];
$completed = isset($input['completed']) ? (int)$input['completed'] : 0;
$userId = $_SESSION['user_id'];

// Validate movie exists
$movie = getMovieById($movieId);
if (!$movie) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Movie not found']);
    exit();
}

try {
    if (updateWatchHistory($userId, $movieId, $watchTime, $completed)) {
        echo json_encode(['success' => true, 'message' => 'Watch progress updated']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update watch progress']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
