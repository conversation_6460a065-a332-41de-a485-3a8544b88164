<?php
require_once 'includes/functions.php';

// Get movie ID from URL
$movieId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$movieId) {
    header('Location: index.php');
    exit();
}

// Get movie details
$movie = getMovieById($movieId);

if (!$movie) {
    header('Location: index.php');
    exit();
}

$pageTitle = $movie->title;
$pageDescription = substr($movie->description, 0, 160) . '...';

// Update watch history if user is logged in
if (isLoggedIn()) {
    updateWatchHistory($_SESSION['user_id'], $movieId);
}

require_once 'includes/header.php';
?>

<div class="movie-detail">
    <!-- Movie Hero Section -->
    <section class="movie-hero" style="background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7)), url('assets/images/<?php echo $movie->backdrop_image ?? 'default-backdrop.jpg'; ?>') center/cover;">
        <div class="movie-detail-content">
            <h1 class="movie-detail-title"><?php echo htmlspecialchars($movie->title); ?></h1>
            
            <div class="movie-detail-meta">
                <span><i class="fas fa-calendar"></i> <?php echo $movie->release_year; ?></span>
                <span><i class="fas fa-clock"></i> <?php echo formatDuration($movie->duration); ?></span>
                <span><i class="fas fa-star"></i> <?php echo $movie->rating; ?>/10</span>
                <span><i class="fas fa-tag"></i> <?php echo htmlspecialchars($movie->genre); ?></span>
            </div>
            
            <p class="movie-detail-description"><?php echo htmlspecialchars($movie->description); ?></p>
            
            <div class="hero-buttons">
                <?php if ($movie->video_url): ?>
                <button class="btn btn-primary" onclick="playMovie(<?php echo $movie->id; ?>)">
                    <i class="fas fa-play"></i> Play Movie
                </button>
                <?php endif; ?>

                <?php if ($movie->trailer_url): ?>
                <button class="btn btn-secondary" onclick="playTrailer(<?php echo $movie->id; ?>)">
                    <i class="fas fa-video"></i> Watch Trailer
                </button>
                <?php endif; ?>

                <?php if (isLoggedIn()): ?>
                <button class="btn btn-secondary favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? 'favorited' : ''; ?>"
                        data-movie-id="<?php echo $movie->id; ?>">
                    <i class="<?php echo isMovieInFavorites($_SESSION['user_id'], $movie->id) ? 'fas fa-heart' : 'far fa-heart'; ?>"></i> My List
                </button>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Video Player Section -->
    <section class="video-section" id="videoSection" style="display: none; padding: 2rem 4%; background: #000;">
        <div class="video-container" style="position: relative; max-width: 1200px; margin: 0 auto;">
            <video id="moviePlayer" controls style="width: 100%; height: auto; background: #000;">
                <source src="" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <button class="close-video" onclick="closeVideo()" style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; border: none; padding: 10px; border-radius: 50%; cursor: pointer; font-size: 1.2rem;">✕</button>
        </div>
    </section>

    <!-- Movie Details Section -->
    <section class="movie-info-section" style="padding: 2rem 4%;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <div style="display: grid; grid-template-columns: 300px 1fr; gap: 2rem; margin-bottom: 2rem;">
                <div class="movie-poster-large">
                    <img src="assets/images/<?php echo $movie->poster_image ?? 'default-poster.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($movie->title); ?>" 
                         style="width: 100%; border-radius: 8px;">
                </div>
                
                <div class="movie-details">
                    <h2 style="color: #e50914; margin-bottom: 1rem;">Movie Details</h2>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Title:</strong>
                        <span><?php echo htmlspecialchars($movie->title); ?></span>
                    </div>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Genre:</strong>
                        <span><?php echo htmlspecialchars($movie->genre); ?></span>
                    </div>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Release Year:</strong>
                        <span><?php echo $movie->release_year; ?></span>
                    </div>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Duration:</strong>
                        <span><?php echo formatDuration($movie->duration); ?></span>
                    </div>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Rating:</strong>
                        <span>⭐ <?php echo $movie->rating; ?>/10</span>
                    </div>
                    
                    <div class="detail-row" style="margin-bottom: 1rem;">
                        <strong style="color: #b3b3b3; display: inline-block; width: 120px;">Status:</strong>
                        <span style="color: #4CAF50;"><?php echo ucfirst($movie->status); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="movie-description-full">
                <h3 style="color: #e50914; margin-bottom: 1rem;">Synopsis</h3>
                <p style="line-height: 1.8; color: #b3b3b3;"><?php echo nl2br(htmlspecialchars($movie->description)); ?></p>
            </div>
        </div>
    </section>

    <!-- Related Movies Section -->
    <?php
    $relatedMovies = searchMovies($movie->genre);
    $relatedMovies = array_filter($relatedMovies, function($m) use ($movie) {
        return $m->id != $movie->id;
    });
    $relatedMovies = array_slice($relatedMovies, 0, 10);
    ?>
    
    <?php if (!empty($relatedMovies)): ?>
    <section class="movie-section">
        <h2 class="section-title">More Like This</h2>
        <div class="movie-row">
            <?php foreach ($relatedMovies as $relatedMovie): ?>
            <div class="movie-card" data-movie-id="<?php echo $relatedMovie->id; ?>">
                <img src="assets/images/<?php echo $relatedMovie->poster_image ?? 'default-poster.jpg'; ?>" 
                     alt="<?php echo htmlspecialchars($relatedMovie->title); ?>" 
                     class="movie-poster">
                <div class="movie-info">
                    <h3 class="movie-title"><?php echo htmlspecialchars($relatedMovie->title); ?></h3>
                    <div class="movie-meta">
                        <span><?php echo $relatedMovie->release_year; ?></span>
                        <span><?php echo formatDuration($relatedMovie->duration); ?></span>
                        <span>⭐ <?php echo $relatedMovie->rating; ?></span>
                    </div>
                    <?php if (isLoggedIn()): ?>
                    <button class="favorite-btn <?php echo isMovieInFavorites($_SESSION['user_id'], $relatedMovie->id) ? 'favorited' : ''; ?>" 
                            data-movie-id="<?php echo $relatedMovie->id; ?>"
                            style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 1.5rem; cursor: pointer;">
                        <?php echo isMovieInFavorites($_SESSION['user_id'], $relatedMovie->id) ? '❤️' : '🤍'; ?>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php endif; ?>
</div>

<script>
function playMovie(movieId) {
    const videoSection = document.getElementById('videoSection');
    const moviePlayer = document.getElementById('moviePlayer');
    const videoSource = moviePlayer.querySelector('source');
    
    // Set video source (in a real app, this would be the actual video URL)
    videoSource.src = `assets/videos/movie_${movieId}.mp4`;
    moviePlayer.load();
    
    // Show video section
    videoSection.style.display = 'block';
    videoSection.scrollIntoView({ behavior: 'smooth' });
    
    // Play video
    moviePlayer.play();
    
    // Update watch progress periodically
    moviePlayer.addEventListener('timeupdate', function() {
        if (this.currentTime > 0) {
            updateWatchProgress(movieId, this.currentTime, this.duration);
        }
    });
}

function playTrailer(movieId) {
    const videoSection = document.getElementById('videoSection');
    const moviePlayer = document.getElementById('moviePlayer');
    const videoSource = moviePlayer.querySelector('source');
    
    // Set trailer source
    videoSource.src = `assets/videos/trailer_${movieId}.mp4`;
    moviePlayer.load();
    
    // Show video section
    videoSection.style.display = 'block';
    videoSection.scrollIntoView({ behavior: 'smooth' });
    
    // Play trailer
    moviePlayer.play();
}

function closeVideo() {
    const videoSection = document.getElementById('videoSection');
    const moviePlayer = document.getElementById('moviePlayer');
    
    moviePlayer.pause();
    videoSection.style.display = 'none';
}

// Update watch progress function (from script.js)
function updateWatchProgress(movieId, currentTime, duration) {
    const watchTime = Math.floor(currentTime);
    const completed = currentTime / duration > 0.9 ? 1 : 0;

    fetch('ajax/update_watch_progress.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            movie_id: movieId,
            watch_time: watchTime,
            completed: completed
        })
    })
    .catch(error => {
        console.error('Error updating watch progress:', error);
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>
