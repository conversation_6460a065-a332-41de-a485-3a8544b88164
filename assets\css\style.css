/* Netflix-like Movie Website Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    background-color: #141414;
    color: #ffffff;
    line-height: 1.6;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.7) 10%, transparent);
    z-index: 1000;
    padding: 15px 4%;
    transition: background-color 0.4s;
}

.header.scrolled {
    background-color: #141414;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    color: #e50914;
    text-decoration: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: #ffffff;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #b3b3b3;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
}

.search-input {
    background: rgba(0,0,0,0.75);
    border: 1px solid #333;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    width: 250px;
}

.search-input:focus {
    outline: none;
    border-color: #e50914;
}

.profile-dropdown {
    position: relative;
}

.profile-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #141414;
    border: 1px solid #333;
    border-radius: 4px;
    min-width: 150px;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 10px 15px;
    color: white;
    text-decoration: none;
    border-bottom: 1px solid #333;
}

.dropdown-menu a:hover {
    background: #333;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://via.placeholder.com/1920x1080/000000/FFFFFF?text=Featured+Movie') center/cover;
    display: flex;
    align-items: center;
    padding: 0 4%;
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.4;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.btn-primary {
    background: #ffffff;
    color: #000000;
}

.btn-primary:hover {
    background: rgba(255,255,255,0.75);
}

.btn-secondary {
    background: rgba(109,109,110,0.7);
    color: #ffffff;
}

.btn-secondary:hover {
    background: rgba(109,109,110,0.4);
}

/* Movie Sections */
.movie-section {
    padding: 2rem 4%;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.movie-row {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
}

.movie-row::-webkit-scrollbar {
    height: 8px;
}

.movie-row::-webkit-scrollbar-track {
    background: #333;
}

.movie-row::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
}

.movie-card {
    flex: 0 0 200px;
    position: relative;
    cursor: pointer;
    transition: transform 0.3s;
}

.movie-card:hover {
    transform: scale(1.05);
}

.movie-poster {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 4px;
}

.movie-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 1rem;
    border-radius: 0 0 4px 4px;
}

.movie-title {
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.movie-meta {
    font-size: 0.8rem;
    color: #b3b3b3;
}

/* Auth Forms */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(rgba(0,0,0,0.75), rgba(0,0,0,0.75)), url('https://via.placeholder.com/1920x1080/000000/FFFFFF?text=Netflix+Background') center/cover;
}

.auth-form {
    background: rgba(0,0,0,0.75);
    padding: 3rem;
    border-radius: 4px;
    width: 100%;
    max-width: 450px;
}

.auth-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    background: #333;
    border: 1px solid #333;
    border-radius: 4px;
    color: white;
    font-size: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: #e50914;
    background: #454545;
}

.btn-auth {
    width: 100%;
    background: #e50914;
    color: white;
    padding: 15px;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 1rem;
}

.btn-auth:hover {
    background: #f40612;
}

.auth-link {
    text-align: center;
    color: #b3b3b3;
}

.auth-link a {
    color: white;
    text-decoration: none;
}

.auth-link a:hover {
    text-decoration: underline;
}

/* Alert Messages */
.alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Movie Detail Page */
.movie-detail {
    padding-top: 80px;
}

.movie-hero {
    height: 70vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7));
    display: flex;
    align-items: end;
    padding: 0 4% 4% 4%;
}

.movie-detail-content {
    max-width: 600px;
}

.movie-detail-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.movie-detail-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #b3b3b3;
}

.movie-detail-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .movie-card {
        flex: 0 0 150px;
    }
    
    .movie-poster {
        height: 225px;
    }
    
    .auth-form {
        padding: 2rem;
        margin: 1rem;
    }
    
    .search-input {
        width: 200px;
    }
}

@media (max-width: 480px) {
    .movie-card {
        flex: 0 0 120px;
    }
    
    .movie-poster {
        height: 180px;
    }
    
    .search-input {
        width: 150px;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
}
