<?php
session_start();
require_once 'config/database.php';

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1;
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// Redirect if not admin
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: index.php');
        exit();
    }
}

// Sanitize input data
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Generate random string
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

// Format duration (minutes to hours:minutes)
function formatDuration($minutes) {
    $hours = floor($minutes / 60);
    $mins = $minutes % 60;
    if ($hours > 0) {
        return $hours . 'h ' . $mins . 'm';
    }
    return $mins . 'm';
}

// Get user by ID
function getUserById($userId) {
    global $database;
    $database->query('SELECT * FROM users WHERE id = :id');
    $database->bind(':id', $userId);
    return $database->single();
}

// Get all movies
function getAllMovies($limit = null) {
    global $database;
    $query = 'SELECT * FROM movies WHERE status = "active" ORDER BY created_at DESC';
    if ($limit) {
        $query .= ' LIMIT ' . $limit;
    }
    $database->query($query);
    return $database->resultset();
}

// Get featured movies
function getFeaturedMovies() {
    global $database;
    $database->query('SELECT * FROM movies WHERE featured = 1 AND status = "active" ORDER BY created_at DESC');
    return $database->resultset();
}

// Get movies by category
function getMoviesByCategory($categoryId, $limit = null) {
    global $database;
    $query = 'SELECT m.* FROM movies m 
              JOIN movie_categories mc ON m.id = mc.movie_id 
              WHERE mc.category_id = :category_id AND m.status = "active" 
              ORDER BY m.created_at DESC';
    if ($limit) {
        $query .= ' LIMIT ' . $limit;
    }
    $database->query($query);
    $database->bind(':category_id', $categoryId);
    return $database->resultset();
}

// Search movies
function searchMovies($searchTerm) {
    global $database;
    $database->query('SELECT * FROM movies WHERE (title LIKE :search OR description LIKE :search OR genre LIKE :search) AND status = "active" ORDER BY title ASC');
    $database->bind(':search', '%' . $searchTerm . '%');
    return $database->resultset();
}

// Get movie by ID
function getMovieById($movieId) {
    global $database;
    $database->query('SELECT * FROM movies WHERE id = :id AND status = "active"');
    $database->bind(':id', $movieId);
    return $database->single();
}

// Get all categories
function getAllCategories() {
    global $database;
    $database->query('SELECT * FROM categories ORDER BY name ASC');
    return $database->resultset();
}

// Check if movie is in user favorites
function isMovieInFavorites($userId, $movieId) {
    global $database;
    $database->query('SELECT id FROM user_favorites WHERE user_id = :user_id AND movie_id = :movie_id');
    $database->bind(':user_id', $userId);
    $database->bind(':movie_id', $movieId);
    return $database->single() ? true : false;
}

// Add movie to favorites
function addToFavorites($userId, $movieId) {
    global $database;
    $database->query('INSERT INTO user_favorites (user_id, movie_id) VALUES (:user_id, :movie_id)');
    $database->bind(':user_id', $userId);
    $database->bind(':movie_id', $movieId);
    return $database->execute();
}

// Remove movie from favorites
function removeFromFavorites($userId, $movieId) {
    global $database;
    $database->query('DELETE FROM user_favorites WHERE user_id = :user_id AND movie_id = :movie_id');
    $database->bind(':user_id', $userId);
    $database->bind(':movie_id', $movieId);
    return $database->execute();
}

// Get user favorites
function getUserFavorites($userId) {
    global $database;
    $database->query('SELECT m.* FROM movies m 
                      JOIN user_favorites uf ON m.id = uf.movie_id 
                      WHERE uf.user_id = :user_id AND m.status = "active" 
                      ORDER BY uf.created_at DESC');
    $database->bind(':user_id', $userId);
    return $database->resultset();
}

// Update watch history
function updateWatchHistory($userId, $movieId, $watchTime = 0, $completed = 0) {
    global $database;
    $database->query('INSERT INTO watch_history (user_id, movie_id, watch_time, completed) 
                      VALUES (:user_id, :movie_id, :watch_time, :completed) 
                      ON DUPLICATE KEY UPDATE 
                      watch_time = :watch_time, completed = :completed, last_watched = CURRENT_TIMESTAMP');
    $database->bind(':user_id', $userId);
    $database->bind(':movie_id', $movieId);
    $database->bind(':watch_time', $watchTime);
    $database->bind(':completed', $completed);
    return $database->execute();
}

// Get user watch history
function getUserWatchHistory($userId, $limit = 10) {
    global $database;
    $database->query('SELECT m.*, wh.watch_time, wh.completed, wh.last_watched 
                      FROM movies m 
                      JOIN watch_history wh ON m.id = wh.movie_id 
                      WHERE wh.user_id = :user_id AND m.status = "active" 
                      ORDER BY wh.last_watched DESC 
                      LIMIT :limit');
    $database->bind(':user_id', $userId);
    $database->bind(':limit', $limit);
    return $database->resultset();
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

// Upload file function
function uploadFile($file, $uploadDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return false;
    }

    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    if ($file['size'] > 5000000) { // 5MB limit
        return false;
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->file($file['tmp_name']);
    
    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];

    $ext = array_search($mimeType, $allowedMimes, true);
    if ($ext === false) {
        return false;
    }

    $fileName = sprintf('%s.%s', sha1_file($file['tmp_name']), $ext);
    $uploadPath = $uploadDir . '/' . $fileName;

    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return false;
    }

    return $fileName;
}
?>
