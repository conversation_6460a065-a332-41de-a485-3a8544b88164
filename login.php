<?php
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);

    // Validation
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Check user credentials
        global $database;
        $database->query('SELECT * FROM users WHERE email = :email');
        $database->bind(':email', $email);
        $user = $database->single();

        if ($user && verifyPassword($password, $user->password)) {
            // Login successful
            $_SESSION['user_id'] = $user->id;
            $_SESSION['username'] = $user->username;
            $_SESSION['email'] = $user->email;
            $_SESSION['full_name'] = $user->full_name;
            $_SESSION['profile_image'] = $user->profile_image;
            $_SESSION['is_admin'] = $user->is_admin;

            // Set remember me cookie if requested
            if ($remember) {
                $token = generateRandomString(32);
                setcookie('remember_token', $token, time() + (86400 * 30), '/'); // 30 days
                
                // Store token in database (you might want to create a remember_tokens table)
                $database->query('UPDATE users SET remember_token = :token WHERE id = :id');
                $database->bind(':token', hash('sha256', $token));
                $database->bind(':id', $user->id);
                $database->execute();
            }

            setFlashMessage('success', 'Welcome back, ' . $user->full_name . '!');
            
            // Redirect to intended page or dashboard
            $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
            header('Location: ' . $redirect);
            exit();
        } else {
            $error = 'Invalid email or password.';
        }
    }
}

$pageTitle = 'Sign In';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - NetflixApp</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-form">
            <h1 class="auth-title">Sign In</h1>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" id="email" name="email" class="form-input" 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                           required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" id="password" name="password" class="form-input" required>
                </div>

                <div class="form-group" style="display: flex; align-items: center; gap: 10px;">
                    <input type="checkbox" id="remember" name="remember" style="width: auto;">
                    <label for="remember" style="margin: 0; font-size: 0.9rem; color: #b3b3b3;">Remember me</label>
                </div>

                <button type="submit" class="btn-auth">Sign In</button>
            </form>

            <div class="auth-link">
                <p>New to NetflixApp? <a href="register.php">Sign up now</a></p>
            </div>

            <div class="auth-link" style="margin-top: 1rem;">
                <p><a href="forgot-password.php">Forgot your password?</a></p>
            </div>

            <div class="auth-link" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #333;">
                <p style="font-size: 0.8rem; color: #666;">
                    Demo Credentials:<br>
                    Email: <EMAIL><br>
                    Password: admin123
                </p>
            </div>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
</body>
</html>
